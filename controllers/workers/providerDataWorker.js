const providerDataRepository = require('../../repositories/providerDataRepository');
const { parseDate } = require('../../utils/dates');
const {
  parseDataPoints,
  headerToModelEntries,
  ratingMapper,
  seniorityAbbreviations,
  industryAbbreviations,
  regionAbbreviations,
} = require('../../utils/providerDataUtils');

async function parseProviderData(readInterface) {
  console.log('parseProviderData: Function started');

  // Parse
  const headers = [];
  let headerLine = true;
  let chunk = [];
  let chunkLength = 1;
  let totalLinesProcessed = 0;
  let totalRowsSkipped = 0;
  let totalRowsAdded = 0;

  try {
    console.log('parseProviderData: Starting to iterate through CSV lines');

    for await (const line of readInterface) {
      totalLinesProcessed++;

      // Log progress every 1000 lines
      if (totalLinesProcessed % 1000 === 0) {
        console.log(
          `parseProviderData: Processed ${totalLinesProcessed} lines, ${totalRowsAdded} rows added, ${totalRowsSkipped} rows skipped`,
        );
      }

      try {
        const data = line.split('|');

        if (headerLine) {
          console.log('parseProviderData: Processing header line');
          // Get header
          for (let i = 0; i < data.length; i++) {
            headers.push(data[i].trim());
          }
          console.log(
            `parseProviderData: Found ${headers.length} headers: ${headers.slice(0, 5).join(', ')}${
              headers.length > 5 ? '...' : ''
            }`,
          );
          headerLine = false;
        } else {
          let parsedData = {};

          // Get row values
          const dataJSON = {};
          for (let i = 0, len = headers.length; i < len; i++) {
            dataJSON[headers[i]] = data[i] ? data[i].trim() : '';
          }

          try {
            // Set values that need no parsing
            for (let i = 0, len = headerToModelEntries.length; i < len; i++) {
              const [key, value] = headerToModelEntries[i];
              parsedData[value] = dataJSON[key];
            }

            parsedData.rating = ratingMapper[parsedData.rating];

            //Abbreviate values
            parsedData.seniority = seniorityAbbreviations[parsedData.seniority];
            parsedData.industryGroup = industryAbbreviations[parsedData.industryGroup];
            parsedData.region = regionAbbreviations[parsedData.region];

            // if any of these isn't expected through provider data constants then discard the row
            if (!parsedData.rating || !parsedData.seniority || !parsedData.industryGroup || !parsedData.region) {
              totalRowsSkipped++;
              if (totalRowsSkipped <= 5) {
                console.log(
                  `parseProviderData: Skipping row ${totalLinesProcessed} - missing required fields. Rating: ${parsedData.rating}, Seniority: ${parsedData.seniority}, Industry: ${parsedData.industryGroup}, Region: ${parsedData.region}`,
                );
              }
              continue;
            }

            // Setting values that need parsing
            // Parse Date
            try {
              parsedData.issueDate = parseDate(dataJSON['DATE']);
            } catch (dateError) {
              console.error(
                `parseProviderData: Error parsing date for line ${totalLinesProcessed}:`,
                dateError.message,
                'Date value:',
                dataJSON['DATE'],
              );
              totalRowsSkipped++;
              continue;
            }

            // Parse data points
            try {
              const parsedDataPoints = parseDataPoints(JSON.parse(dataJSON['VALUES']));
              parsedData = { ...parsedData, ...parsedDataPoints };
            } catch (valuesError) {
              console.error(
                `parseProviderData: Error parsing VALUES JSON for line ${totalLinesProcessed}:`,
                valuesError.message,
                'VALUES value:',
                dataJSON['VALUES']?.substring(0, 100) + '...',
              );
              totalRowsSkipped++;
              continue;
            }

            chunk.push(parsedData);
            totalRowsAdded++;

            if (chunkLength % 10000 === 0) {
              console.log(
                `parseProviderData: Saving chunk of ${chunk.length} records to database (chunk ${Math.floor(
                  chunkLength / 10000,
                )})`,
              );
              try {
                await providerDataRepository.createProviderData(chunk, { logging: false });
                console.log(
                  `parseProviderData: Successfully saved chunk ${Math.floor(chunkLength / 10000)} to database`,
                );
              } catch (dbError) {
                console.error(
                  `parseProviderData: Error saving chunk ${Math.floor(chunkLength / 10000)} to database:`,
                  dbError.message,
                );
                throw dbError;
              }
              chunkLength = 1;
              chunk = [];
            }

            chunkLength += 1;
          } catch (rowError) {
            console.error(`parseProviderData: Error processing row ${totalLinesProcessed}:`, rowError.message);
            totalRowsSkipped++;
            continue;
          }
        }
      } catch (lineError) {
        console.error(
          `parseProviderData: Error processing line ${totalLinesProcessed}:`,
          lineError.message,
          'Line content:',
          line.substring(0, 100) + '...',
        );
        totalRowsSkipped++;
        continue;
      }
    }

    console.log(
      `parseProviderData: Finished processing all lines. Total: ${totalLinesProcessed}, Added: ${totalRowsAdded}, Skipped: ${totalRowsSkipped}`,
    );

    if (chunk.length !== 0) {
      console.log(`parseProviderData: Saving final chunk of ${chunk.length} records to database`);
      try {
        await providerDataRepository.createProviderData(chunk, { logging: false });
        console.log('parseProviderData: Successfully saved final chunk to database');
      } catch (dbError) {
        console.error('parseProviderData: Error saving final chunk to database:', dbError.message);
        throw dbError;
      }
    }

    console.log('parseProviderData: Function completed successfully');
  } catch (error) {
    console.error('parseProviderData: Fatal error occurred:', error.message);
    console.error('parseProviderData: Error stack:', error.stack);
    throw error;
  }
}

module.exports = {
  parseProviderData,
};
