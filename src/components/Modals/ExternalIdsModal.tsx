import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { cashPoolTopCurrencyAccountSelector, updateAccounts } from 'reducers/cashPoolTopCurrencyAccount.slice';
import { Button, Modal, TextInput } from 'ui';

type ExternalIdsModalPropsType = {
  dataTestId?: string;
  participant: any;
  onHide: () => void;
};

function ExternalIdsModal({ participant, dataTestId, onHide }: ExternalIdsModalPropsType) {
  const [externalIds, setExternalIds] = useState<string>('');
  const [excludedIds, setExcludedIds] = useState<string>('');
  const dispatch = useDispatch();
  const topCurrencyAccount = useSelector(cashPoolTopCurrencyAccountSelector);
  const account = topCurrencyAccount?.accounts?.find((account: any) => account?.companyId === participant?.id);

  const handleOnSubmitClick = () => {
    const externalIdsSplitted = externalIds
      ?.split(',')
      ?.map((id: string) => ({ externalId: id.trim() }))
      .filter((item) => item.externalId !== '');
    const excludedIdsSplitted = excludedIds
      ?.split(',')
      ?.map((id: string) => ({ excludedId: id.trim() }))
      .filter((item) => item.excludedId !== '');
    const data = {
      ...account,
      externalIds: externalIdsSplitted,
      excludedIds: excludedIdsSplitted,
    };
    dispatch(updateAccounts({ ...data, value: true }));
    onHide();
  };

  useEffect(() => {
    if (account) {
      setExternalIds(account.externalIds?.map(({ externalId }: { externalId: string }) => externalId).join(',') || '');
      setExcludedIds(account.excludedIds?.map(({ excludedId }: { excludedId: string }) => excludedId).join(',') || '');
    }

    return () => {
      setExternalIds('');
      setExcludedIds('');
    };
  }, [account]);

  if (!participant) return null;

  return (
    <Modal
      actionButtons={<Button text="Submit" onClick={handleOnSubmitClick} dataTestId={dataTestId} />}
      title={`Edit External & Excluded IDs of ${account?.participant?.company?.name || account?.name}`}
      width="s"
      onHide={onHide}
    >
      <TextInput
        inputType="float"
        allowNegatives
        label="External IDs (separated by comma)"
        width="fullWidth"
        value={externalIds}
        onChange={setExternalIds}
      />
      <TextInput
        inputType="float"
        allowNegatives
        label="Excluded IDs (separated by comma)"
        width="fullWidth"
        value={excludedIds}
        onChange={setExcludedIds}
      />
    </Modal>
  );
}

export default ExternalIdsModal;
