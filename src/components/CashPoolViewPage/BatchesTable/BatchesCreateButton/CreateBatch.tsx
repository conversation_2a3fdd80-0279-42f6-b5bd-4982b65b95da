import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';

import { createBatchFromSftpData, getCashPool, getCashPoolBatches, getCashPoolBatchRange } from 'api';
import { format } from 'date-fns';
import { cashPoolSelector, setCashPool, updateCashPoolTrigger } from 'reducers/cashPool.slice';
import { Box, Button, DateInput, Modal } from 'ui';
import { showToast } from 'ui/components/Toast';
import { errorHandler } from 'utils/errors';

type CreateBatchModalPropsType = {
  isModalShowing: boolean;
  setIsModalShowing: React.Dispatch<React.SetStateAction<boolean>>;
  startDate: Date | null;
  setStartDate: React.Dispatch<React.SetStateAction<Date | null>>;
  endDate: Date | null;
  setEndDate: React.Dispatch<React.SetStateAction<Date | null>>;
  setBatches: React.Dispatch<React.SetStateAction<undefined>>;
};

type BatchRangeResponseType = {
  startDate: Date;
  endDate: Date;
};

const CreateBatchModal = ({
  isModalShowing,
  setIsModalShowing,
  startDate,
  setStartDate,
  endDate,
  setEndDate,
  setBatches,
}: CreateBatchModalPropsType) => {
  const { cashPoolId } = useParams<{ cashPoolId: string }>();
  const dispatch = useDispatch();
  const { type } = useSelector(cashPoolSelector);
  const [isBatchRunning, setIsBatchRunning] = useState<boolean>(false);

  const onModalHide = () => {
    setIsModalShowing(false);
    setStartDate(null);
    setEndDate(null);
  };

  const onCreateBatch = async () => {
    try {
      setIsBatchRunning(true);
      await createBatchFromSftpData({
        cashPoolId,
        cashPoolType: type,
        data: {
          startDate: format(new Date(String(startDate)), 'yyyy-MM-dd'),
          endDate: format(new Date(String(endDate)), 'yyyy-MM-dd'),
        },
      });
      const [cashPool] = await Promise.all([
        getCashPool({ cashPoolId }),
        getCashPoolBatches({ cashPoolId }).then(setBatches),
      ]);
      dispatch(setCashPool(cashPool));
      dispatch(updateCashPoolTrigger({}));
      showToast('Cash successfully pooled.');
    } catch (err) {
      errorHandler(err);
    } finally {
      setIsBatchRunning(false);
      onModalHide();
    }
  };

  useEffect(() => {
    if (!isModalShowing) return;

    getCashPoolBatchRange({ cashPoolId })
      .then(({ startDate, endDate }: BatchRangeResponseType) => {
        setStartDate(startDate);
        setEndDate(endDate);
      })
      .catch(errorHandler);
  }, [isModalShowing, cashPoolId, setEndDate, setStartDate]);

  if (!isModalShowing) return null;

  return (
    <Modal
      actionButtons={
        <Button
          text="Create"
          loading={isBatchRunning}
          size="l"
          disabled={!startDate || !endDate}
          onClick={onCreateBatch}
        />
      }
      title="Select date range for cash pool"
      width="m"
      onHide={onModalHide}
    >
      <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(3, 1fr)' }}>
        <DateInput label="Start Date (Book date)" value={startDate} onChange={setStartDate} />
        <DateInput label="End Date (Book date)" value={endDate} onChange={setEndDate} />
      </Box>
    </Modal>
  );
};

type CreateBatchPropsType = {
  setBatches: React.Dispatch<React.SetStateAction<undefined>>;
};

const CreateBatch = ({ setBatches }: CreateBatchPropsType) => {
  const [isModalShowing, setIsModalShowing] = useState<boolean>(false);
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);

  return (
    <>
      <Button iconLeft="add" size="s" text="Run Batch" variant="secondary" onClick={() => setIsModalShowing(true)} />
      <CreateBatchModal
        isModalShowing={isModalShowing}
        setIsModalShowing={setIsModalShowing}
        startDate={startDate}
        setStartDate={setStartDate}
        endDate={endDate}
        setEndDate={setEndDate}
        setBatches={setBatches}
      />
    </>
  );
};

export default CreateBatch;
