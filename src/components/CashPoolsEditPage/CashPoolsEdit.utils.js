import _ from 'lodash';

import { isCashPoolFormValid, updateField } from '~/reducers/cashPool.slice';
import { isTopCurrencyAccountValid, updateAccounts } from '~/reducers/cashPoolTopCurrencyAccount.slice';

const mapAccounts = (accounts, isUpdate) => {
  return accounts.map(
    ({
      id,
      companyId,
      creditInterestRate,
      debitInterestRate,
      balance,
      externalIds,
      excludedIds,
      generateInterestStatementData,
    }) => {
      let shouldGenerateInterestStatementData = generateInterestStatementData;
      if (isUpdate) shouldGenerateInterestStatementData = generateInterestStatementData ?? false;

      return {
        cashPoolAccountId: id,
        companyId,
        creditInterestRate,
        debitInterestRate,
        generateInterestStatementData: shouldGenerateInterestStatementData,
        balance: balance ?? null,
        externalIds: externalIds?.map(({ externalId }) => externalId),
        excludedIds: excludedIds?.map(({ excludedId }) => excludedId),
      };
    }
  );
};

const mapNotionalAccounts = (accounts) => {
  return accounts.map(({ companyId, creditInterestRate, debitInterestRate, currency }) => ({
    companyId,
    creditInterestRate,
    debitInterestRate,
    currency,
  }));
};

/**
 * Creating the physical cash pool.
 * 1. Fill top info (name, currency, country, leader, rates, expenses)
 * 2. Fill functional analysis
 * 3. Pick participants from the table
 *    3.1. It uses cashPoolTopCurrencyAccount.slice to store participants (accounts)
 *    3.2. After clicking Next accounts from cashPoolTopCurrencyAccount.slice are mapped to the create dto
 */
export const getCreatePhysicalCashPoolDto = (cashPool, accounts) => {
  const physicalCashPoolDto = _.pick(cashPool, [
    'type',
    'name',
    'currencies',
    'country',
    'leaderId',
    'creditInterestRate',
    'debitInterestRate',
    'interestType',
    'operatingCost',
    'operatingCostMarkup',
    'nordicPhysicalRiskAnalysisAnswers',
    'totalRisk',
    'assessment',
    'overnightRate',
    'estimateRatesCalculationLog',
  ]);
  physicalCashPoolDto.riskAnalysisAnswers = physicalCashPoolDto.nordicPhysicalRiskAnalysisAnswers;
  delete physicalCashPoolDto.nordicPhysicalRiskAnalysisAnswers;
  physicalCashPoolDto.totalRisk = physicalCashPoolDto.totalRisk / 100;

  physicalCashPoolDto.accounts = mapAccounts(accounts);

  return physicalCashPoolDto;
};

export const getUpdatePhysicalCashPoolDto = (cashPool, accounts, shouldCreateAuditTrail) => {
  const physicalCashPoolDto = _.pick(cashPool, [
    'name',
    'creditInterestRate',
    'debitInterestRate',
    'interestType',
    'operatingCost',
    'operatingCostMarkup',
    'nordicPhysicalRiskAnalysisAnswers',
    'totalRisk',
    'assessment',
    'overnightRate',
    'estimateRatesCalculationLog',
  ]);

  physicalCashPoolDto.riskAnalysisAnswers = physicalCashPoolDto.nordicPhysicalRiskAnalysisAnswers;
  delete physicalCashPoolDto.nordicPhysicalRiskAnalysisAnswers;
  physicalCashPoolDto.totalRisk = physicalCashPoolDto.totalRisk / 100;

  physicalCashPoolDto.accounts = mapAccounts(accounts, true);

  physicalCashPoolDto.shouldCreateAuditTrail = shouldCreateAuditTrail;

  return physicalCashPoolDto;
};

/** Same as creating the Physical, see above */
export const getCreateNotionalCashPoolDto = (cashPool, accounts) => {
  const notionalCashPoolDto = _.pick(cashPool, [
    'type',
    'name',
    'currencies',
    'country',
    'leaderId',
    'creditInterestRate',
    'debitInterestRate',
    'interestType',
    'operatingCost',
    'operatingCostMarkup',
    'notionalRiskAnalysisAnswers',
    'totalRisk',
    'assessment',
    'overnightRate',
  ]);
  notionalCashPoolDto.riskAnalysisAnswers = notionalCashPoolDto.notionalRiskAnalysisAnswers;
  delete notionalCashPoolDto.notionalRiskAnalysisAnswers;
  notionalCashPoolDto.totalRisk = notionalCashPoolDto.totalRisk / 100;

  notionalCashPoolDto.accounts = mapNotionalAccounts(accounts);

  return notionalCashPoolDto;
};

export const getUpdateNotionalCashPoolDto = (cashPool, accounts, shouldCreateAuditTrail) => {
  const notionalCashPoolDto = _.pick(cashPool, [
    'name',
    'creditInterestRate',
    'debitInterestRate',
    'interestType',
    'operatingCost',
    'operatingCostMarkup',
    'notionalRiskAnalysisAnswers',
    'totalRisk',
    'assessment',
    'overnightRate',
  ]);

  notionalCashPoolDto.riskAnalysisAnswers = notionalCashPoolDto.notionalRiskAnalysisAnswers;
  delete notionalCashPoolDto.notionalRiskAnalysisAnswers;
  notionalCashPoolDto.totalRisk = notionalCashPoolDto.totalRisk / 100;

  notionalCashPoolDto.accounts = mapNotionalAccounts(accounts);

  notionalCashPoolDto.shouldCreateAuditTrail = shouldCreateAuditTrail;

  return notionalCashPoolDto;
};

/**
 * Creating the nordic cash pool.
 * 1. Fill top info (name, currency, country, leader, expenses)
 * 2. Fill functional analysis
 * 3. Create Top Currency Account (uses cashPoolTopCurrencyAccount.slice)
 *    3.1. Fill top info (name, currency, rates...)
 *    3.2. Choose participants that is accounts in that top account
 *    3.3. After clicking Save that Top Account is added to the cashPool.slice topCurrencyAccounts
 */
export const getCreateNordicCashPoolDto = (cashPool) => {
  const nordicCashPoolDto = _.pick(cashPool, [
    'type',
    'name',
    'currencies',
    'country',
    'leaderId',
    'operatingCost',
    'operatingCostMarkup',
    'nordicPhysicalRiskAnalysisAnswers',
    'totalRisk',
    'assessment',
    'topCurrencyAccounts',
  ]);
  nordicCashPoolDto.riskAnalysisAnswers = nordicCashPoolDto.nordicPhysicalRiskAnalysisAnswers;
  delete nordicCashPoolDto.nordicPhysicalRiskAnalysisAnswers;
  nordicCashPoolDto.totalRisk = nordicCashPoolDto.totalRisk / 100;

  nordicCashPoolDto.topCurrencyAccounts = nordicCashPoolDto.topCurrencyAccounts.map((topCurrencyAccount) => ({
    name: topCurrencyAccount.name,
    currency: topCurrencyAccount.currency,
    interestType: topCurrencyAccount.interestType,
    creditInterestRate: topCurrencyAccount.creditInterestRate,
    debitInterestRate: topCurrencyAccount.debitInterestRate,
    overnightRate: topCurrencyAccount.overnightRate,
    accounts: mapAccounts(topCurrencyAccount.accounts),
  }));

  return nordicCashPoolDto;
};

export const getUpdateNordicCashPoolDto = (cashPool, shouldCreateAuditTrail) => {
  const nordicCashPoolDto = _.pick(cashPool, [
    'name',
    'operatingCost',
    'operatingCostMarkup',
    'nordicPhysicalRiskAnalysisAnswers',
    'totalRisk',
    'assessment',
    'topCurrencyAccounts',
  ]);
  nordicCashPoolDto.riskAnalysisAnswers = nordicCashPoolDto.nordicPhysicalRiskAnalysisAnswers;
  delete nordicCashPoolDto.nordicPhysicalRiskAnalysisAnswers;
  nordicCashPoolDto.totalRisk = nordicCashPoolDto.totalRisk / 100;

  nordicCashPoolDto.topCurrencyAccounts = nordicCashPoolDto.topCurrencyAccounts.map((topCurrencyAccount) => ({
    id: topCurrencyAccount.id,
    name: topCurrencyAccount.name,
    currency: topCurrencyAccount.currency,
    interestType: topCurrencyAccount.interestType,
    creditInterestRate: topCurrencyAccount.creditInterestRate,
    debitInterestRate: topCurrencyAccount.debitInterestRate,
    overnightRate: topCurrencyAccount.overnightRate,
    accounts: mapAccounts(topCurrencyAccount.accounts),
  }));

  nordicCashPoolDto.shouldCreateAuditTrail = shouldCreateAuditTrail;

  return nordicCashPoolDto;
};

export const getRequiredPhysicalCashPoolFields = (cashPool) =>
  _.pick(cashPool, [
    'name',
    'type',
    'country',
    'interestType',
    'leaderId',
    'currencies',
    'creditInterestRate',
    'debitInterestRate',
    'operatingCost',
    'operatingCostMarkup',
    cashPool.interestType === 'float' && 'overnightRate',
    'assessment',
  ]);

export const getRequiredNotionalCashPoolFields = (cashPool) =>
  _.pick(cashPool, [
    'name',
    'type',
    'country',
    'interestType',
    'leaderId',
    'currencies',
    'creditInterestRate',
    'debitInterestRate',
    'operatingCost',
    'operatingCostMarkup',
    cashPool.interestType === 'float' && 'overnightRate',
    'assessment',
  ]);

export const getRequiredNordicCashPoolFields = (cashPool) =>
  _.pick(cashPool, [
    'name',
    'type',
    'country',
    'leaderId',
    'currencies',
    'operatingCost',
    'operatingCostMarkup',
    cashPool.interestType === 'float' && 'overnightRate',
    'assessment',
  ]);

export const validationErrorMessages = {
  allParticipantsMustHaveCirAndDir: 'All included participants must have credit and debit interest rates.',
  cirPositiveNumber: 'Credit interest rates must be valid positive numbers.',
  dirPositiveNumber: 'Debit interest rates must be valid positive numbers.',
  validThreeLetterCurrencyNotation: 'Use a valid three letter currency notation.',
};

export const isPhysicalCashPoolValid = ({ cashPool, topCurrencyAccount, dispatch }) => {
  const isFixed = topCurrencyAccount.interestType === 'fixed';
  const word = isFixed ? 'rate' : 'spread';

  const errors = isCashPoolFormValid(cashPool);
  if (Object.keys(errors).length) {
    dispatch(updateField({ errors, showErrors: true }));
    throw new Error('Some fields are not valid.');
  }
  /** isTopCurrencyAccount is false because this is participants table and not nordic top currency account */
  const topAccountErrors = isTopCurrencyAccountValid({ topCurrencyAccount, cashPool, isTopCurrencyAccount: false });
  if (topAccountErrors.accounts) {
    for (const account of topAccountErrors.accounts) {
      dispatch(updateAccounts({ ...account }));
    }
    throw new Error(`One or more credit or debit interest ${word}s are not compliant. Check the participants table.`);
  }
};

export const isNotionalCashPoolValid = ({ cashPool, topCurrencyAccount, dispatch }) => {
  const isFixed = topCurrencyAccount.interestType === 'fixed';
  const word = isFixed ? 'rate' : 'spread';

  const errors = isCashPoolFormValid(cashPool);
  if (Object.keys(errors).length) {
    dispatch(updateField({ errors, showErrors: true }));
    throw new Error('Some fields are not valid.');
  }
  /** isTopCurrencyAccount is false because this is participants table and not nordic top currency account */
  const topAccountErrors = isTopCurrencyAccountValid({ topCurrencyAccount, cashPool, isTopCurrencyAccount: false });
  if (topAccountErrors.accounts) {
    for (const account of topAccountErrors.accounts) {
      dispatch(updateAccounts({ ...account }));
    }

    let accountErrors = {};
    topAccountErrors.accounts.forEach((account) => (accountErrors = { ...accountErrors, ...account.error }));
    if (accountErrors.creditInterestRate || accountErrors.debitInterestRate) {
      throw new Error(`One or more credit or debit interest ${word}s are not compliant. Check the participants table.`);
    }
    if (accountErrors.currency) {
      throw new Error('Company currencies are required. Check the participants table.');
    }
  }
};
