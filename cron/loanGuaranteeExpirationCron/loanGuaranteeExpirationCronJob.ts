if (typeof global.AbortController === 'undefined') {
  // @ts-ignore
  global.AbortController = require('abort-controller');
}

import { differenceInCalendarDays } from 'date-fns';
import { Op } from 'sequelize';
import { parentPort } from 'worker_threads';

import { rolesEnum } from '../../enums';
import models from '../../models';
import { clientRepository, guaranteeRepository, loanRepository, userRepository } from '../../repositories';
import {
  ClientWithNumberOfUsersType,
  GuaranteeType,
  LoanType,
  ReceivingUserNotificationType,
  UserWithClientType,
} from '../../types';

import {
  handleGuaranteeNotificationCreation,
  handleLoanNotificationCreation,
} from './loanGuaranteeExpirationCronJobUtils';
import { sendLoanGuaranteeExpirationEmail } from './mailService';

const { sequelize } = models;
const THREE_MONTHS_IN_DAYS = 92;

const isThreeMonthsUntilExpire = (expireDate: Date, today: Date): boolean => {
  return differenceInCalendarDays(new Date(expireDate), today) === THREE_MONTHS_IN_DAYS;
};

const run = async () => {
  await sequelize.transaction(async () => {
    const clients: ClientWithNumberOfUsersType[] = await clientRepository.getClients();
    const today = new Date();

    for (const { id: clientId } of clients) {
      const repositoryObject = {
        where: { clientId, isPortfolio: true },
        order: null,
        paranoid: true,
        limit: null,
      };
      const [loans, guarantees, clientUsers] = await Promise.all<[LoanType[], GuaranteeType[], UserWithClientType[]]>([
        loanRepository.getLoans(repositoryObject),
        guaranteeRepository.getGuarantees(repositoryObject),
        userRepository.getUsers({ clientId, role: { [Op.or]: [rolesEnum.ADMIN, rolesEnum.SUPERADMIN] } }),
      ]);

      const loansAboutToExpire: LoanType[] = [];
      const createLoanNotificationPromises: Promise<ReceivingUserNotificationType[]>[] = [];
      for (const loan of loans) {
        if (isThreeMonthsUntilExpire(new Date(loan.maturityDate), today)) {
          createLoanNotificationPromises.push(handleLoanNotificationCreation(loan, clientUsers));
          loansAboutToExpire.push(loan);
        }
      }

      const guaranteesAboutToExpire: GuaranteeType[] = [];
      const createGuaranteeNotificationPromises: Promise<ReceivingUserNotificationType[]>[] = [];
      for (const guarantee of guarantees) {
        if (isThreeMonthsUntilExpire(new Date(guarantee.terminationDate), today)) {
          createGuaranteeNotificationPromises.push(handleGuaranteeNotificationCreation(guarantee, clientUsers));
          guaranteesAboutToExpire.push(guarantee);
        }
      }

      await Promise.all([
        ...createLoanNotificationPromises,
        ...createGuaranteeNotificationPromises,
        sendLoanGuaranteeExpirationEmail(clientUsers, loansAboutToExpire, guaranteesAboutToExpire),
      ]);
    }
  });
  parentPort?.postMessage('Loan/Guarantee expiration cron job finished successfully.');
};

run();
