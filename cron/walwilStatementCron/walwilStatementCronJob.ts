if (typeof global.AbortController === 'undefined') {
  // @ts-ignore
  global.AbortController = require('abort-controller');
}

import models from '../../models';
import { nordeaStatementJob } from './banks/nordea';
import { sebStatementJob } from './banks/seb';
const { sequelize } = models;

(async () => {
  await sequelize.transaction(async () => {
    await nordeaStatementJob();
    await sebStatementJob();
  });
})();
